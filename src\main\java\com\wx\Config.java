package com.wx;

import org.springframework.beans.factory.annotation.Autowired;



public class Config {
	public static String APPID="";     //开放平台
	public static String APPSECRET=""; //开放平台
	public static String APPID1="";     //公众号
	public static String APPSECRET1="1"; //公众号
	public static String TOKEN="";
	public static String CORESERVICEIMPL="";
	
	/**/
	//70BROKERS配置  #dc1009   #FF3F42
	
	
	public static String CRM_TITLE="DEMO CRM SYSTEM";    //浏览器左上角的title
	public static String CRM_LOGO1="../logo.png";   //浏览器左上角的logo
	public static String CRM_LOGO2="../logo.png";   //登录页面的logo  
	public static String HEADER_SCRIPT_STATUS="0";    //header 脚本开关   1是开
	public static String JS_ID1="G-453CDKCVRB";
	public static String JS_ID2="2431287773823837";
	public static String JS_ID3="GTM-KPHMDBJ";
	public static String FOOTER_SCRIPT_STATUS="0";    //footer 脚本1开关  1是开
	public static String JS_KEY="52f53700-b75e-43e9-92cc-959837c82faa";
	public static String FOOTER2_SCRIPT_STATUS="0";    //footer 脚本2开关  1是开
	public static String JS2_ID="10789";
	public static String JS2_KEY="47052d907ac9ee095fc1b5065cf43aad";
	
	
	public static String WEB_TRADER_STATUS="0";   //webtrader 开关  1是开
	public static String WEB_TRADER_URL="";   //webtrader 的url
	public static String SUPPORT_STATUS="0";   //support 开关  1是开
	public static String SUPPORT_URL="";   //webtrader 的url
	
	public static String BANNER_IMG="../dist/banner.png";
	public static String BANNER_URL="";
	
	public static String CRM_VERSION="1.0.0";
	public static String CRM_LICENSED="DEMO Tech  International Limited";
	
	public static String APP3_STATUS="0";   //只有70brokers才开启这个
	public static String APP_URL1="";
	public static String APP_URL2="";
	public static String APP_URL3="";
	public static String APP_URL4="";
	public static String APP_URL5="";
	public static String APP_URL6="";
	public static String APP_URL7="";
	public static String APP_URL8="";
	
	
	
	
	
	
	
}
