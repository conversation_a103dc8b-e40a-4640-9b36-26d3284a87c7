
package com.ews.crm.controller;

import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;



@RestController
@RequestMapping("/admin/transferInfo")
public class TransferInfoController {
	@Autowired
	private TransferInfoService transferInfoService;

	@Autowired
	private UserInfoService userInfoService;

	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;
	
	@Autowired
	private OperLogService operLogService;

	@Autowired
	private ConstantConfig constantConfig;

	
	
   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	TransferInfo query  = new TransferInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferOut"))) {
               	query.setTransferOut(Integer.parseInt(request.getParameter("transferOut").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferIn"))) {
               	query.setTransferIn(Integer.parseInt(request.getParameter("transferIn").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outStatus"))) {
               	query.setOutStatus(Integer.parseInt(request.getParameter("outStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inStatus"))) {
               	query.setInStatus(Integer.parseInt(request.getParameter("inStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferStatus"))) {
               	query.setTransferStatus(Integer.parseInt(request.getParameter("transferStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outOrderId"))) {
               	query.setOutOrderId(request.getParameter("outOrderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inOrderId"))) {
               	query.setInOrderId(request.getParameter("inOrderId").trim());
        	}
        	 List userList=new ArrayList();
        	  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			
	            			 //1.查出自己及名下所有的代理
	            			 
	            			 User user_query=new User();
	            			 user_query.setSortStr(uu.getSortStr());
	            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	            			
	            			 
	            			 //2.遍历代理查出所有的crm用户ID
	            			 
	            			
	            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
	            			 
	            			 UserInfo ui_self_query=new UserInfo();
	            			 ui_self_query.setParentId(uu.getUserId());
	            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
	            			 for(int m=0;m<ui_self.getContent().size();m++) {
	            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
	            				 userList.add(ui_1.getId());
	            				 
	            			 }
	            			 if(ui_self.getContent().size()<=0) {
	            				 userList.add(999999L);
	            			 }
	            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
	            			 
	            			 for(int n=0;n<ul.getContent().size();n++) {
	            				 User user_1=ul.getContent().get(n);
	            				 UserInfo ui_self_query_2=new UserInfo();
	            				 ui_self_query_2.setParentId(user_1.getUserId());
		            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
		            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
		            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
		            				 userList.add(ui_2.getId());
		            			 }
	            			 }
	            			 query.setUserInfoList(userList);
	            		 }
	            	 }else {
	            		 userList.add(-99999999L);
	            		 query.setUserInfoList(userList);
	            	 }
	             }else {
	            	 userList.add(-99999999L);
            		 query.setUserInfoList(userList);
	             }
	             
        	
        	Page<TransferInfo> pages = transferInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<TransferInfo> transferInfos = pages.getContent();
        	for(int i=0;i<transferInfos.size();i++) {
        		TransferInfo entity  = transferInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
        		 
        		 UserInfo ui=this.userInfoService.findById(entity.getUserId());
        		 entity.setUserInfo(ui);
        	}
        	
        	datas.put("items", transferInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list2")
    public ResponseData list2(HttpServletRequest request) {
    	try {
        	TransferInfo query  = new TransferInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferOut"))) {
               	query.setTransferOut(Integer.parseInt(request.getParameter("transferOut").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferIn"))) {
               	query.setTransferIn(Integer.parseInt(request.getParameter("transferIn").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outStatus"))) {
               	query.setOutStatus(Integer.parseInt(request.getParameter("outStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inStatus"))) {
               	query.setInStatus(Integer.parseInt(request.getParameter("inStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferStatus"))) {
               	query.setTransferStatus(Integer.parseInt(request.getParameter("transferStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outOrderId"))) {
               	query.setOutOrderId(request.getParameter("outOrderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inOrderId"))) {
               	query.setInOrderId(request.getParameter("inOrderId").trim());
        	}
        	query.setAuditStatus(0);//待审核的同名转账申请
        	Page<TransferInfo> pages = transferInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<TransferInfo> transferInfos = pages.getContent();
        	for(int i=0;i<transferInfos.size();i++) {
        		TransferInfo entity  = transferInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 UserInfo ui=this.userInfoService.findById(entity.getUserId());
        		 entity.setUserInfo(ui);
        	}
        	
        	datas.put("items", transferInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			TransferInfo transferInfo = new TransferInfo();
        	transferInfo.setUserId(data.getLong("userId"));
        	transferInfo.setTransferOut(data.getInteger("transferOut"));
        	transferInfo.setTransferIn(data.getInteger("transferIn"));
        	transferInfo.setTransferAmount(data.getDouble("transferAmount"));
        	transferInfo.setOutStatus(data.getInteger("outStatus"));
        	transferInfo.setInStatus(data.getInteger("inStatus"));
        	transferInfo.setTransferStatus(data.getInteger("transferStatus"));
        	transferInfo.setAuditStatus(data.getInteger("auditStatus"));
        	transferInfo.setOutOrderId(data.getString("outOrderId"));
        	transferInfo.setInOrderId(data.getString("inOrderId"));
			Result re = transferInfoService.saveOrUpdate(transferInfo);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(transferInfo);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			TransferInfo transferInfo = this.transferInfoService.findById(id);
				if (transferInfo != null) {
					transferInfo.setUserId(data.getLong("userId"));
					transferInfo.setTransferOut(data.getInteger("transferOut"));
					transferInfo.setTransferIn(data.getInteger("transferIn"));
					transferInfo.setTransferAmount(data.getDouble("transferAmount"));
					transferInfo.setOutStatus(data.getInteger("outStatus"));
					transferInfo.setInStatus(data.getInteger("inStatus"));
					transferInfo.setTransferStatus(data.getInteger("transferStatus"));
					transferInfo.setAuditStatus(data.getInteger("auditStatus"));
					transferInfo.setOutOrderId(data.getString("outOrderId"));
					transferInfo.setInOrderId(data.getString("inOrderId"));
					Result re = transferInfoService.saveOrUpdate(transferInfo);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = transferInfoService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(6);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}
	
	
	@GetMapping( "/auditTransferInfo")
	public ResponseData auditTransferInfo(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				
				TransferInfo transferInfo = this.transferInfoService.findById(id);
				transferInfo.setAuditStatus(new Integer(request.getParameter("type")));
				this.transferInfoService.saveOrUpdate(transferInfo);
				
				//操作日志 -begin
				User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
				OperLog olog=new OperLog();
				olog.setBusId(id);
				olog.setBusType(6);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
				olog.setOperId(loginUser_log.getUserId());
				olog.setOperName(loginUser_log.getUsername());
				olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
				this.operLogService.saveOrUpdate(olog);
				//操作日志 -end
				return ResponseDataUtil.buildSuccess(); 
				
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}

    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = transferInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}

	/**
	* 导出报表
	* @param request
	* @return
	*/
    @PostMapping("/exportExcel")
    public ResponseData exportExcel(HttpServletRequest request) {
		    JSONObject datas = new JSONObject();
    	try {


			String filename=new Date().getTime()+".xls";
			String path=constantConfig.getFileStoreUrl();
	   String[] headers = {  "申请时间", "处理时间","转出账号","转入账号","所属CRM用户","CRM账号","转账时间","转账金额","审核状态"};
	   // 声明一个工作薄
	   HSSFWorkbook workbook = new HSSFWorkbook();
	   // 生成一个表格
	   HSSFSheet sheet = workbook.createSheet();
	   // 设置表格默认列宽度为15个字节
	   //sheet.setDefaultColumnWidth((short)3);
	   sheet.setColumnWidth(0,(short)15*256);
	   sheet.setColumnWidth(1, (short)15*256);
	   sheet.setColumnWidth(2,(short)15*256);
	   sheet.setColumnWidth(3, (short)15*256);
	   sheet.setColumnWidth(4, (short)15*256);
	   sheet.setColumnWidth(5, (short)15*256);
	   sheet.setColumnWidth(6, (short)15*256);
	   sheet.setColumnWidth(7, (short)15*256);
	   sheet.setColumnWidth(8, (short)15*256);
	
	   sheet.setDefaultRowHeight((short)400);
	  
		HSSFRow row = sheet.createRow(0);
		for (short i = 0; i < headers.length; i++) {
			HSSFCell cell = row.createCell(i);
			HSSFRichTextString text = new HSSFRichTextString(headers[i]);
			HSSFCellStyle style = workbook.createCellStyle();
				//设置背景颜色
				style.setFillForegroundColor((short)10);
			cell.setCellStyle(style);
				cell.setCellValue(text);
		}
			int index = 0;

		   
        	TransferInfo query  = new TransferInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferOut"))) {
               	query.setTransferOut(Integer.parseInt(request.getParameter("transferOut").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferIn"))) {
               	query.setTransferIn(Integer.parseInt(request.getParameter("transferIn").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outStatus"))) {
               	query.setOutStatus(Integer.parseInt(request.getParameter("outStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inStatus"))) {
               	query.setInStatus(Integer.parseInt(request.getParameter("inStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("transferStatus"))) {
               	query.setTransferStatus(Integer.parseInt(request.getParameter("transferStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("outOrderId"))) {
               	query.setOutOrderId(request.getParameter("outOrderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("inOrderId"))) {
               	query.setInOrderId(request.getParameter("inOrderId").trim());
        	}
        	 List userList=new ArrayList();
        	  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			
	            			 //1.查出自己及名下所有的代理
	            			 
	            			 User user_query=new User();
	            			 user_query.setSortStr(uu.getSortStr());
	            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	            			
	            			 
	            			 //2.遍历代理查出所有的crm用户ID
	            			 
	            			
	            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
	            			 
	            			 UserInfo ui_self_query=new UserInfo();
	            			 ui_self_query.setParentId(uu.getUserId());
	            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
	            			 for(int m=0;m<ui_self.getContent().size();m++) {
	            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
	            				 userList.add(ui_1.getId());
	            				 
	            			 }
	            			 if(ui_self.getContent().size()<=0) {
	            				 userList.add(999999L);
	            			 }
	            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
	            			 
	            			 for(int n=0;n<ul.getContent().size();n++) {
	            				 User user_1=ul.getContent().get(n);
	            				 UserInfo ui_self_query_2=new UserInfo();
	            				 ui_self_query_2.setParentId(user_1.getUserId());
		            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
		            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
		            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
		            				 userList.add(ui_2.getId());
		            			 }
	            			 }
	            			 query.setUserInfoList(userList);
	            		 }
	            	 }else {
	            		 userList.add(-99999999L);
	            		 query.setUserInfoList(userList);
	            	 }
	             }else {
	            	 userList.add(-99999999L);
            		 query.setUserInfoList(userList);
	             }
	             
        	
        	Page<TransferInfo> pages = transferInfoService.findAll(0,2000, sort, sortBy, query);
        	
        	List<TransferInfo> transferInfos = pages.getContent();
        	for(int i=0;i<transferInfos.size();i++) {
				index++;
				row = sheet.createRow(index);
        		TransferInfo entity  = transferInfos.get(i);
				UserInfo ui=this.userInfoService.findById(entity.getUserId());
				row.createCell(0).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
				row.createCell(1).setCellValue(new HSSFRichTextString(entity.getGmtModified().toString()));
				row.createCell(2).setCellValue(new HSSFRichTextString(entity.getTransferOut().toString()));
				row.createCell(3).setCellValue(new HSSFRichTextString(entity.getTransferIn().toString()));
				row.createCell(4).setCellValue(new HSSFRichTextString(ui.getFullname()));
				row.createCell(5).setCellValue(new HSSFRichTextString(ui.getUserName()));
				row.createCell(6).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
				row.createCell(7).setCellValue(new HSSFRichTextString(entity.getTransferAmount().toString()));
				if(entity.getAuditStatus().intValue()==0){
					row.createCell(8).setCellValue(new HSSFRichTextString("待审核"));
				}else if(entity.getAuditStatus().intValue()==1){
					row.createCell(8).setCellValue(new HSSFRichTextString("已通过"));
				}else if(entity.getAuditStatus().intValue()==2){
					row.createCell(8).setCellValue(new HSSFRichTextString("被驳回"));
				}
				
        	}
        	
        	 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}

	


}

