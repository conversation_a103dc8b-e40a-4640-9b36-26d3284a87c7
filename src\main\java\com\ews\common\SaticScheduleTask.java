package com.ews.common;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ReckbackInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ReckbackInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.UserService;

@Configuration      //1.主要用于标记配置类，兼备Component的效果。
@EnableScheduling   // 2.开启定时任务
@EnableAsync 
public class SaticScheduleTask {
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	
	@Autowired
	private UserService userService;
	@Autowired
	private ReckbackInfoService reckbackInfoService;
	//是否开启自动轮询功能 true 开启   false  关闭
	/**/
	private boolean bl1=true;   //余额，
	private boolean bl8=true;   //预付款                //如果是HL，则设置为false
	private boolean bl4=true;   //查询持仓                     //如果是HL，则设置为false
	private boolean bl5=true;   //查询历史订单
	private boolean mainSwitch=true;    //总开关
	private String newPort="8070";
	
   // @Scheduled(fixedRate=60000)
	@Async
    @Scheduled(fixedRate=300000L)//每5分钟查询一次
	public void configureTasks() {
    	if(bl1&&mainSwitch) {
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
		MyWebsocketClient4Balance client= new MyWebsocketClient4Balance(new URI("ws://"+ss.getApiAddress()+":"+newPort), new Draft_6455(), null, 0);
		client.setTradeAccountService(this.tradeAccountService);
		    client.connect();
			Thread.sleep(300);
			if (client.getReadyState().equals(READYSTATE.OPEN)) {
				System.out.println("getBalance   "+new Date()+" "+client.getReadyState());
				this.getBalance(client,ss.getBackup1());
			 }
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
    
   
    
    //查询余额、信用、净值
    public void getBalance(MyWebsocketClient4Balance client,String roleUser) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "getuserlistinfo");
			map.put("reqid", String.valueOf(new Date().getTime()));
			map.put("login", new Integer(roleUser).intValue());
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
	}
    
    
    // @Scheduled(fixedRate=600000,initialDelay=20000)
    @Async
    @Scheduled(fixedRate=3600000L,initialDelay=20000)//每小时查询一次
    public void configureTasks1() {
    	if(bl8&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,100000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
		MyWebsocketClient4Balance2 client2= new MyWebsocketClient4Balance2(new URI("ws://"+ss.getApiAddress()+":"+newPort), new Draft_6455(), null, 0);
		        client2.setTradeAccountService(this.tradeAccountService);
		    	client2.connect();
		    	Thread.sleep(300);
				if (client2.getReadyState().equals(READYSTATE.OPEN)) {
					 System.out.println("getBalance2   "+new Date()+" "+client2.getReadyState());
					 java.util.List list2=new ArrayList();
						for(int i=0;i<pages2.getContent().size();i++) {
				    		TradeAccount entity  = pages2.getContent().get(i);
				    		HashMap mm1=new HashMap();
				    		mm1.put("login", new Integer(entity.getTradeId()).intValue());
				    		list2.add(mm1);
				    		if((i+1)%80==0) {
				    			this.getBalance2(list2,client2);
				    			Thread.sleep(50L);
				    			list2=new ArrayList();
				    		}
				     }
						this.getBalance2(list2,client2);
		    	}
				
			}catch(Exception e) {
				System.out.println(e);
		    }
    	}
    }
    
 
    //可用预付款、已用预付款
     public void getBalance2(java.util.List l,MyWebsocketClient4Balance2 client) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "marginleveluserinfo");
			map.put("reqid", String.valueOf(new Date().getTime()));
			map.put("loginItem", l);
			map.put("count", l.size());
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
	}
  
     
     //查询持仓   每4分钟查询一次，延迟2分钟
    // @Scheduled(fixedRate=240000,initialDelay=129000)
     @Async
     @Scheduled(fixedRate=3600000L,initialDelay=329000)//每小时查询一次
     public void configureTasks2() {
    	if(bl4&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,5000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		try {
				MyWebsocketClient4Hold client= new MyWebsocketClient4Hold(new URI("ws://"+ss.getApiAddress()+":8050"), new Draft_6455(), null, 0);
				client.setTradeAccountService(this.tradeAccountService);
				client.setOrderInfoService(this.orderInfoService);
				  client.connect();
				  Thread.sleep(300);
					if (client.getReadyState().equals(READYSTATE.OPEN)) {
						System.out.println("queryHold   "+new Date()+" "+client.getReadyState());
						for(int i=0;i<pages2.getContent().size();i++) {
				    		TradeAccount entity  = pages2.getContent().get(i);
				    		this.queryHold(entity,client);
				    	}
					 }
		    	
			}catch(Exception e) {
				System.out.println(e);
						
			}
    	}
    }
    
    public void queryHold(TradeAccount tradeAccount,MyWebsocketClient4Hold client) {
		try {
		    HashMap map=new HashMap();
		    map.put("reqtype", "ordersuserinfo");
			map.put("reqid", String.valueOf(new Date().getTime()));
			map.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
		    JSONObject jsonObj=new JSONObject(map);
		    client.send(jsonObj.toString());
		}catch(Exception e) {
			System.out.println(e);
		}
				
	}
   
    //查询历史   每10分钟查询一次，延迟4分钟
    // @Scheduled(fixedRate=600000,initialDelay=245000)    
    @Async
    @Scheduled(fixedRate=60000*10L,initialDelay=160000)  //每小时查询一次 3600000L   //HL   600000
    public void configureTasks3() {
    	if(bl5&&mainSwitch) {
    	TradeAccount query=new TradeAccount();
    	query.setIsAvailable(1);
    	Page<TradeAccount> pages2 = tradeAccountService.findAll(0,5000,"id","asc",query);
    	ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
    	try {
    	MyWebsocketClient4History client= new MyWebsocketClient4History(new URI("ws://"+ss.getApiAddress()+":8070"), new Draft_6455(), null, 0);
  		client.setTradeAccountService(this.tradeAccountService);
  		client.setOrderInfoService(this.orderInfoService);
  		client.connect();
  		Thread.sleep(300);
  		System.out.println("totals:"+pages2.getContent().size());
		if (client.getReadyState().equals(READYSTATE.OPEN)) {
			System.out.println("queryHistory   "+new Date()+" "+client.getReadyState());
			for(int i=0;i<pages2.getContent().size();i++) {
				
				if((i+1)%100==0) {//专门针对RH 交易用户过多做的优化处理
					Thread.sleep(1000*30);
				}
				System.out.println("cur:"+i);
	    		TradeAccount entity  = pages2.getContent().get(i);

				// 判断当前时间和修改时间比较，如果超过24小时则执行解绑-BEGIN
				Date currentTime = new Date();
				Date modifiedTime = entity.getGmtModified();
				
				if(modifiedTime != null) {
					long timeDifferenceInMillis = currentTime.getTime() - modifiedTime.getTime();
					long twentyFourHoursInMillis = 24 * 60 * 60 * 1000L; // 24小时的毫秒数
					
					if(timeDifferenceInMillis > twentyFourHoursInMillis) {
						entity.setUserId(null);
						entity.setUserAccount("");
						entity.setIsAvailable(-2);
						entity.setBalance1(0d);
						entity.setBalance2(0d);
						entity.setBalance3(0d);
						entity.setBalance4(0d);
						entity.setBalance5(0d);
						entity.setBalance6(0d);
						entity.setBalance7(0d);
						this.tradeAccountService.saveOrUpdate(entity);
						
					}
				}
				// 判断当前时间和修改时间比较，如果超过24小时则执行解绑-END

	    		if(client.getReadyState().equals(READYSTATE.OPEN)) {
	    		this.queryHistory(entity,client);
	    		}else {
	    			client.reconnect();
	    			Thread.sleep(300);
	    			this.queryHistory(entity,client);
	    		}
	    		
	    		
	    	}
		 }
  		}catch(Exception e) {
  			System.out.println(e);
  		}
    	}
    }
    
    public void queryHistory(TradeAccount tradeAccount,MyWebsocketClient4History client) {
  		ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
  		try {
  		
  		    HashMap map=new HashMap();
  		    map.put("reqtype", "historyorderinfo");
  			map.put("reqid", String.valueOf(new Date().getTime()));
  			map.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
  			OrderInfo order_query=new OrderInfo();
			order_query.setLoginId(String.valueOf(tradeAccount.getTradeId()));
			order_query.setStatus(2);
			List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	ll.add(2);
	    	ll.add(3);
	    	ll.add(4);
	    	ll.add(5);
	    	ll.add(6);
	    	order_query.setTypeList(ll);
			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,1, "closeTime","desc", order_query);
			if(oi_page.getContent().size()>0) {//有记录
				map.put("fromtime", new Long((oi_page.getContent().get(0).getCloseTime())-3700L));
				  //System.out.println("1:"+oi_page.getContent().get(0).getLoginId().toString()+" "+map.get("fromtime")+"   "+((new Date().getTime()/1000)+86400L)+"   ");
			}else {//没有交易信息
				map.put("fromtime", new Long((new Date().getTime()/1000)-3700L));
			//	map.put("fromtime", new Long((tradeAccount.getGmtCreate().getTime()/1000)-3700L));
				  //System.out.println("2:"+tradeAccount.getTradeId()+" "+map.get("fromtime")+"   "+((new Date().getTime()/1000)+86400L)+"   ");
			}
  			map.put("endtime", (new Date().getTime()/1000)+86400L);  //结束时间增加一个小时计算
  		    JSONObject jsonObj=new JSONObject(map);
  		   client.send(jsonObj.toString());
  		  
  		}catch(Exception e) {
  			System.out.println(e);
  		}
  				
  	}
    
}